name: <PERSON><PERSON> and Push Docker Image

on:
  workflow_dispatch:
    inputs:
      version:
        description: "镜像版本号"
        required: false
        default: "latest"
      environment:
        description: "部署环境"
        required: false
        default: "production"
        type: choice
        options:
          - production
          - staging
          - development

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      # 设置 QEMU 以支持多平台构建
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2

      # 设置 Docker Buildx
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
        with:
          driver-opts: |
            image=moby/buildkit:master
            network=host

      - name: Docker Login
        run: docker login -u ${{ vars.DOCKER_USER }} -p ${{ secrets.DOCKER_PWD }} ${{ vars.DOCKER_REG }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: hivenexus/hivechat
          tags: |
            type=raw,value=${{ github.event.inputs.version || 'latest' }}
            type=raw,value=latest

      - name: Docker Build and <PERSON>ush
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          platforms: linux/amd64,linux/arm64
          labels: ${{ steps.meta.outputs.labels }}
          tags: ${{ steps.meta.outputs.tags }}
          cache-from: type=gha,scope=${{ github.workflow }}-${{ github.ref }}
          cache-to: type=gha,mode=max,scope=${{ github.workflow }}-${{ github.ref }}
          build-args: |
            ENVIRONMENT=${{ github.event.inputs.environment || 'production' }}
